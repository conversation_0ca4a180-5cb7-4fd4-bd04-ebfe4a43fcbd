import { createAbortError } from '../error'

export function abortable<T>(promise: Promise<T>, signal?: AbortSignal): Promise<T> {
    if (!signal) {
        return promise
    }

    if (signal.aborted) {
        return Promise.reject(signal.reason ?? createAbortError())
    }

    return new Promise<T>(async (resolve, reject) => {
        let isSettled = false

        const onAbort = () => {
            if (!isSettled) {
                isSettled = true
                signal.removeEventListener('abort', onAbort)
                reject(signal.reason ?? createAbortError())
            }
        }

        signal.addEventListener('abort', onAbort)

        try {
            const value = await promise

            if (!isSettled) {
                isSettled = true
                signal.removeEventListener('abort', onAbort)
                resolve(value)
            }
        } catch (error) {
            if (!isSettled) {
                isSettled = true
                signal.removeEventListener('abort', onAbort)
                reject(error)
            }
        }
    })
}
